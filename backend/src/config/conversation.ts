// MiCA Conversation Configuration
// Central configuration for therapist and patient personas, conversation flow, and prompts
// NOTE: Prompts are now centralized in JSON configuration files. This file maintains structure for compatibility.

import { promptConfigService } from '../services/prompt-config-service.js';

export interface ConversationConfig {
  therapist: TherapistConfig;
  patient: PatientConfig;
  conversation: ConversationFlowConfig;
  messaging: MessagingConfig;
}

export interface TherapeuticApproachConfig {
  enableDualApproach: boolean;
  readinessThresholds: {
    cbtMinimum: number; // Minimum score for CBT (typically 8)
    miMaximum: number;  // Maximum score for MI (typically 7)
  };
  approachPreferences: {
    defaultApproach: 'CBT' | 'MI' | 'AUTO';
    allowApproachSwitching: boolean;
    techniqueVariation: boolean;
  };
  readinessCalculation: {
    sentimentWeight: number;
    motivationWeight: number;
    engagementWeight: number;
  };
}

export interface TherapistConfig {
  persona: {
    name: string;
    background: string;
    approach: string;
    specialties: string[];
    communicationStyle: string;
  };
  behavior: {
    empathyLevel: 'high' | 'medium' | 'low';
    directness: 'direct' | 'gentle' | 'indirect';
    questioningStyle: 'open-ended' | 'structured' | 'mixed';
    responseLength: 'brief' | 'moderate' | 'detailed';
  };
  techniques: string[];
  analysisCapabilities: {
    sentiment: boolean;
    motivation: boolean;
    engagement: boolean;
    riskAssessment: boolean;
  };
  therapeuticApproaches: TherapeuticApproachConfig;
  prompts: {
    systemPrompt: string;
    initialGreeting: string;
    responseTemplate: string;
    analysisPrompts: {
      sentiment: string;
      motivation: string;
      engagement: string;
      riskAssessment: string;
    };
  };
}

export interface CognitiveConceptualization {
  coreBeliefs: string[];
  intermediateBeliefs: string[];
  intermediateBeliefsDepression?: string[];
  copingStrategies: string[];
}

export interface PatientEmotions {
  primary: string[];
  secondary?: string[];
  intensityLevels?: { [emotion: string]: 'low' | 'medium' | 'high' };
}

export interface PatientBehaviors {
  maladaptive: string[];
  copingMechanisms: string[];
  behavioralPatterns: string[];
}

export interface PatientConfig {
  id?: string;
  persona: {
    name: string;
    age: number;
    background: string;
    currentSituation: string;
    personalityTraits: string[];
  };
  emotionalState: {
    primaryMood: 'depressed' | 'anxious' | 'neutral' | 'hopeful' | 'frustrated' | 'angry';
    energyLevel: 'low' | 'medium' | 'high';
    openness: 'closed' | 'guarded' | 'open' | 'very_open';
    trustLevel: number; // 0-100
    motivationLevel: 'low' | 'medium' | 'high';
  };
  concerns: string[];
  backstory: string;
  responsePatterns: {
    tendency: 'withdrawn' | 'talkative' | 'defensive' | 'cooperative';
    emotionalExpression: 'suppressed' | 'moderate' | 'expressive';
    detailLevel: 'minimal' | 'moderate' | 'elaborate';
  };
  // Enhanced persona data
  relevantHistory?: string;
  cognitiveConceptualization?: CognitiveConceptualization;
  automaticThoughts?: string[];
  emotions?: PatientEmotions;
  behaviors?: PatientBehaviors;
  conversationalStyle?: string;
  presentingConcerns?: string[];
  sessionContext?: string;
  prompts: {
    systemPrompt: string;
    responseTemplate: string;
    emotionalStatePrompt: string;
    thinkingPrompt: string;
  };
}

export interface ConversationFlowConfig {
  maxTurns: number;
  autoAdvance: boolean;
  responseDelay: {
    therapist: number; // milliseconds
    patient: number; // milliseconds
  };
  turnManagement: {
    allowInterruptions: boolean;
    maxConsecutiveTurns: number;
  };
  progressionRules: {
    buildRapport: boolean;
    exploreIssues: boolean;
    workTowardsSolutions: boolean;
  };
}

export interface PromptTemplates {
  therapist: {
    systemPrompt: string;
    initialGreeting: string;
    responseTemplate: string;
    analysisPrompts: {
      sentiment: string;
      motivation: string;
      engagement: string;
      riskAssessment: string;
    };
  };
  patient: {
    systemPrompt: string;
    responseTemplate: string;
    emotionalStatePrompt: string;
    thinkingPrompt: string;
  };
}

export interface MessagingConfig {
  format: {
    includeTimestamp: boolean;
    includeMetadata: boolean;
    includeThinking: boolean;
  };
  validation: {
    maxMessageLength: number;
    minMessageLength: number;
    allowEmptyMessages: boolean;
  };
  logging: {
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    logToConsole: boolean;
    logToFile: boolean;
  };
}

// Default configuration
export const defaultConversationConfig: ConversationConfig = {
  therapist: {
    persona: {
      name: "Dr. Montri",
      background: "Licensed clinical psychologist with 10 years of experience specializing in cognitive behavioral therapy and motivational interviewing interventions.",
      approach: "Warm, empathetic, and solution-focused with a collaborative therapeutic style.",
      specialties: ["anxiety disorders", "depression", "stress management", "relationship issues"],
      communicationStyle: "Active listening with gentle probing questions and reflective responses."
    },
    behavior: {
      empathyLevel: 'high',
      directness: 'gentle',
      questioningStyle: 'mixed',
      responseLength: 'moderate'
    },
    techniques: [
      "active listening",
      "reflective responses",
      "open-ended questioning",
      "cognitive reframing",
      "mindfulness techniques",
      "validation",
      "summarization"
    ],
    analysisCapabilities: {
      sentiment: true,
      motivation: true,
      engagement: true,
      riskAssessment: true
    },
    therapeuticApproaches: {
      enableDualApproach: true,
      readinessThresholds: {
        cbtMinimum: 8,
        miMaximum: 7
      },
      approachPreferences: {
        defaultApproach: 'AUTO',
        allowApproachSwitching: true,
        techniqueVariation: true
      },
      readinessCalculation: {
        sentimentWeight: 0.3,
        motivationWeight: 0.4,
        engagementWeight: 0.3
      }
    },
    prompts: {
      systemPrompt: '', // Will be loaded from JSON configuration
      initialGreeting: '', // Will be loaded from JSON configuration
      responseTemplate: '', // Will be loaded from JSON configuration
      analysisPrompts: {
        sentiment: '', // Will be loaded from JSON configuration
        motivation: '', // Will be loaded from JSON configuration
        engagement: '', // Will be loaded from JSON configuration
        riskAssessment: '' // Will be loaded from JSON configuration
      }
    }
  },
  patient: {
    persona: {
      name: "Alex",
      age: 28,
      background: "Software developer working remotely, recently moved to a new city",
      currentSituation: "Struggling with work-life balance and social isolation since the move",
      personalityTraits: ["introverted", "analytical", "perfectionist", "self-critical"]
    },
    emotionalState: {
      primaryMood: 'anxious',
      energyLevel: 'low',
      openness: 'guarded',
      trustLevel: 40,
      motivationLevel: 'medium'
    },
    concerns: [
      "work-related stress",
      "difficulty making new friends",
      "imposter syndrome",
      "sleep issues",
      "social anxiety"
    ],
    backstory: "Recently relocated for a new job opportunity but finding it challenging to adapt to the new environment. Has a history of anxiety but hasn't sought professional help before.",
    responsePatterns: {
      tendency: 'withdrawn',
      emotionalExpression: 'suppressed',
      detailLevel: 'minimal'
    },
    prompts: {
      systemPrompt: '', // Will be loaded from JSON configuration
      responseTemplate: '', // Will be loaded from JSON configuration
      emotionalStatePrompt: '', // Will be loaded from JSON configuration
      thinkingPrompt: '' // Will be loaded from JSON configuration
    }
  },
  conversation: {
    maxTurns: 20,
    autoAdvance: true,
    responseDelay: {
      therapist: 2000,
      patient: 3000
    },
    turnManagement: {
      allowInterruptions: false,
      maxConsecutiveTurns: 2
    },
    progressionRules: {
      buildRapport: true,
      exploreIssues: true,
      workTowardsSolutions: true
    }
  },

  messaging: {
    format: {
      includeTimestamp: true,
      includeMetadata: true,
      includeThinking: true
    },
    validation: {
      maxMessageLength: 1000,
      minMessageLength: 10,
      allowEmptyMessages: false
    },
    logging: {
      logLevel: 'debug',
      logToConsole: true,
      logToFile: false
    }
  }
};

/**
 * Create conversation configuration with centralized prompts
 * This function loads prompts from the centralized JSON configuration files
 */
export async function createConversationConfigWithCentralizedPrompts(): Promise<ConversationConfig> {
  try {
    // Load prompts from centralized configuration
    const [
      systemPrompt,
      initialGreeting,
      responseTemplate,
      analysisPrompts
    ] = await Promise.all([
      promptConfigService.getTherapistSystemPrompt(),
      promptConfigService.getInitialGreeting(),
      promptConfigService.getResponseTemplate(),
      promptConfigService.getAnalysisPrompts()
    ]);

    // Create configuration with centralized prompts
    const config = { ...defaultConversationConfig };

    // Update therapist prompts
    config.therapist.prompts = {
      systemPrompt,
      initialGreeting,
      responseTemplate,
      analysisPrompts
    };

    return config;
  } catch (error) {
    console.warn('⚠️ Error loading centralized prompts, using default configuration:', error);
    return defaultConversationConfig;
  }
}

/**
 * Get therapeutic technique prompt from centralized configuration
 */
export async function getTherapeuticTechniquePrompt(approach: string, technique: string): Promise<string> {
  try {
    return await promptConfigService.getTherapeuticTechniquePrompt(approach, technique);
  } catch (error) {
    console.warn(`⚠️ Error loading technique prompt for ${approach}.${technique}:`, error);
    return 'Respond therapeutically with empathy and understanding.';
  }
}

/**
 * Get multi-therapist system prompt with variables
 */
export async function getMultiTherapistSystemPrompt(): Promise<string> {
  try {
    return await promptConfigService.getMultiTherapistSystemPrompt();
  } catch (error) {
    console.warn('⚠️ Error loading multi-therapist system prompt:', error);
    return defaultConversationConfig.therapist.prompts.systemPrompt;
  }
}
